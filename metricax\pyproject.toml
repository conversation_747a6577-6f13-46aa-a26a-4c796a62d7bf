[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "metricax"
version = "0.1.0"
description = "Professional mathematical and statistical toolkit for Python - Bayesian statistics, information theory, and numerical computing"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "MD <PERSON>hoai<PERSON>", email = "mds<PERSON><PERSON><PERSON><PERSON>@gmail.com"}
]
maintainers = [
    {name = "MD <PERSON>aib <PERSON>", email = "<EMAIL>"}
]
requires-python = ">=3.8"
keywords = [
    "mathematics", "statistics", "bayesian", "information-theory",
    "entropy", "machine-learning", "data-science", "probability",
    "numerical-computing", "scientific-computing"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Information Analysis",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Typing :: Typed",
]

# No runtime dependencies - uses only Python standard library
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-benchmark>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "isort>=5.0.0",
    "bandit>=1.7.0",
    "safety>=2.0.0",
]
examples = [
    "jupyter>=1.0.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "pandas>=1.3.0",
    "numpy>=1.21.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]
all = [
    "metricax[dev,examples,docs]"
]

[project.urls]
Homepage = "https://github.com/metricax/metricax"
Documentation = "https://metricax.readthedocs.io"
Repository = "https://github.com/metricax/metricax.git"
"Bug Tracker" = "https://github.com/metricax/metricax/issues"
Changelog = "https://github.com/metricax/metricax/blob/main/CHANGELOG.md"
Discussions = "https://github.com/metricax/metricax/discussions"

[tool.setuptools.packages.find]
where = ["."]
include = ["metricax*"]
exclude = ["tests*", "docs*", "examples*", "notebooks*"]

[tool.setuptools.package-data]
metricax = ["py.typed"]

# Testing configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=metricax",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["metricax"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Code formatting
[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# Import sorting
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["metricax"]
known_third_party = ["pytest", "numpy", "matplotlib", "seaborn", "pandas"]

# Type checking
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

# Coverage configuration
[tool.coverage.run]
source = ["metricax"]
omit = [
    "*/tests/*",
    "*/examples/*",
    "*/notebooks/*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

# Linting configuration
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".eggs",
    "*.egg",
]

# Security scanning
[tool.bandit]
exclude_dirs = ["tests", "examples", "notebooks"]
skips = ["B101", "B601"]
