# MetricaX API Reference 📖

**Complete function reference for all MetricaX mathematical modules**

This document provides detailed API documentation for every function in MetricaX, including parameters, return values, mathematical formulations, and usage examples.

## 🎯 **Bayesian Statistics Module**

### **Import**
```python
import metricax.bayesian as mb
```

---

## **Beta Distributions**

### `beta_pdf(x, alpha, beta)`
Compute the probability density function of the Beta distribution.

**Parameters:**
- `x` (float): Value at which to evaluate PDF, must be in [0, 1]
- `alpha` (float): Shape parameter α > 0
- `beta` (float): Shape parameter β > 0

**Returns:**
- `float`: Probability density at x

**Mathematical Formula:**
```
f(x; α, β) = (Γ(α + β) / (Γ(α) * Γ(β))) * x^(α-1) * (1-x)^(β-1)
```

**Example:**
```python
>>> mb.beta_pdf(0.5, 2, 2)
1.5
>>> mb.beta_pdf(0.3, 1, 1)  # Uniform distribution
1.0
```

---

### `beta_cdf(x, alpha, beta, steps=1000)`
Compute the cumulative distribution function of the Beta distribution.

**Parameters:**
- `x` (float): Upper limit of integration, must be in [0, 1]
- `alpha` (float): Shape parameter α > 0
- `beta` (float): Shape parameter β > 0
- `steps` (int): Number of integration steps (default: 1000)

**Returns:**
- `float`: Cumulative probability P(X ≤ x)

**Mathematical Formula:**
```
F(x; α, β) = ∫₀ˣ f(t; α, β) dt
```

**Example:**
```python
>>> mb.beta_cdf(0.5, 2, 2)
0.5
>>> mb.beta_cdf(1.0, 1, 1)
1.0
```

---

### `beta_mean(alpha, beta)`
Compute the mean of the Beta distribution.

**Parameters:**
- `alpha` (float): Shape parameter α > 0
- `beta` (float): Shape parameter β > 0

**Returns:**
- `float`: Mean of the distribution

**Mathematical Formula:**
```
μ = α / (α + β)
```

**Example:**
```python
>>> mb.beta_mean(2, 2)
0.5
>>> mb.beta_mean(1, 3)
0.25
```

---

### `beta_var(alpha, beta)`
Compute the variance of the Beta distribution.

**Parameters:**
- `alpha` (float): Shape parameter α > 0
- `beta` (float): Shape parameter β > 0

**Returns:**
- `float`: Variance of the distribution

**Mathematical Formula:**
```
σ² = (α * β) / ((α + β)² * (α + β + 1))
```

**Example:**
```python
>>> mb.beta_var(2, 2)
0.05
>>> mb.beta_var(1, 1)
0.08333333333333333
```

---

### `beta_mode(alpha, beta)`
Compute the mode of the Beta distribution.

**Parameters:**
- `alpha` (float): Shape parameter α > 0
- `beta` (float): Shape parameter β > 0

**Returns:**
- `float` or `None`: Mode of the distribution, or None if undefined

**Mathematical Formula:**
```
Mode = (α - 1) / (α + β - 2)  if α > 1 and β > 1
```

**Example:**
```python
>>> mb.beta_mode(2, 2)
0.5
>>> mb.beta_mode(1, 1)  # Returns None
None
```

---

## **Bayes' Theorem**

### `bayes_posterior(prior, likelihood, marginal)`
Compute posterior probability using Bayes' theorem.

**Parameters:**
- `prior` (float): Prior probability P(H)
- `likelihood` (float): Likelihood P(E|H)
- `marginal` (float): Marginal probability P(E)

**Returns:**
- `float`: Posterior probability P(H|E)

**Mathematical Formula:**
```
P(H|E) = P(E|H) * P(H) / P(E)
```

**Example:**
```python
>>> mb.bayes_posterior(0.3, 0.8, 0.5)
0.48
```

---

### `bayes_odds(prior_odds, likelihood_ratio)`
Compute posterior odds using Bayes' theorem in odds form.

**Parameters:**
- `prior_odds` (float): Prior odds P(H) / P(¬H)
- `likelihood_ratio` (float): Likelihood ratio P(E|H) / P(E|¬H)

**Returns:**
- `float`: Posterior odds P(H|E) / P(¬H|E)

**Mathematical Formula:**
```
Posterior Odds = Prior Odds × Likelihood Ratio
```

**Example:**
```python
>>> mb.bayes_odds(1.0, 2.0)
2.0
```

---

## **Conjugate Priors**

### `update_beta_binomial(alpha, beta, successes, failures)`
Update Beta prior with Binomial likelihood.

**Parameters:**
- `alpha` (float): Prior Beta parameter α > 0
- `beta` (float): Prior Beta parameter β > 0
- `successes` (int): Number of observed successes
- `failures` (int): Number of observed failures

**Returns:**
- `tuple`: Updated (alpha_new, beta_new) parameters

**Mathematical Formula:**
```
Beta(α, β) + Binomial(n, k) → Beta(α + k, β + n - k)
```

**Example:**
```python
>>> mb.update_beta_binomial(1, 1, 7, 3)
(8.0, 4.0)
```

---

## 📡 **Information Theory Module**

### **Import**
```python
import metricax.info_theory as it
```

---

## **Entropy Measures**

### `entropy(p, base=2.0)`
Compute Shannon entropy of a probability distribution.

**Parameters:**
- `p` (List[float]): Probability distribution (must sum to 1)
- `base` (float): Logarithm base (default: 2.0 for bits)

**Returns:**
- `float`: Shannon entropy in specified units

**Mathematical Formula:**
```
H(X) = -∑ p(x) * log_base(p(x))
```

**Example:**
```python
>>> it.entropy([0.5, 0.5])
1.0
>>> it.entropy([0.25, 0.25, 0.25, 0.25])
2.0
```

---

### `cross_entropy(p, q, base=2.0)`
Compute cross-entropy between two probability distributions.

**Parameters:**
- `p` (List[float]): True probability distribution
- `q` (List[float]): Predicted/model probability distribution
- `base` (float): Logarithm base (default: 2.0)

**Returns:**
- `float`: Cross-entropy between p and q

**Mathematical Formula:**
```
H(p, q) = -∑ p(x) * log_base(q(x))
```

**Example:**
```python
>>> it.cross_entropy([0.5, 0.5], [0.5, 0.5])
1.0
>>> it.cross_entropy([1.0, 0.0], [0.9, 0.1])
0.152
```

---

### `kl_divergence(p, q, base=2.0)`
Compute Kullback-Leibler divergence.

**Parameters:**
- `p` (List[float]): True probability distribution
- `q` (List[float]): Approximate probability distribution
- `base` (float): Logarithm base (default: 2.0)

**Returns:**
- `float`: KL divergence D_KL(p || q)

**Mathematical Formula:**
```
D_KL(p || q) = ∑ p(x) * log_base(p(x) / q(x))
```

**Example:**
```python
>>> it.kl_divergence([0.5, 0.5], [0.5, 0.5])
0.0
>>> it.kl_divergence([0.8, 0.2], [0.6, 0.4])
0.097
```

---

## **Mutual Information**

### `mutual_information(p_xy, p_x, p_y, base=2.0)`
Compute mutual information I(X; Y).

**Parameters:**
- `p_xy` (List[List[float]]): Joint distribution P(X, Y) as 2D list
- `p_x` (List[float]): Marginal distribution P(X)
- `p_y` (List[float]): Marginal distribution P(Y)
- `base` (float): Logarithm base (default: 2.0)

**Returns:**
- `float`: Mutual information I(X; Y) ≥ 0

**Mathematical Formula:**
```
I(X; Y) = ∑∑ p(x,y) * log(p(x,y) / (p(x) * p(y)))
```

**Example:**
```python
>>> # Independent variables
>>> p_xy = [[0.25, 0.25], [0.25, 0.25]]
>>> p_x = [0.5, 0.5]
>>> p_y = [0.5, 0.5]
>>> it.mutual_information(p_xy, p_x, p_y)
0.0
```

---

## **Distance Measures**

### `hellinger_distance(p, q)`
Compute Hellinger distance between two probability distributions.

**Parameters:**
- `p` (List[float]): First probability distribution
- `q` (List[float]): Second probability distribution

**Returns:**
- `float`: Hellinger distance ∈ [0, 1]

**Mathematical Formula:**
```
H(p, q) = (1/√2) * √(∑(√p(x) - √q(x))²)
```

**Example:**
```python
>>> it.hellinger_distance([0.5, 0.5], [0.5, 0.5])
0.0
>>> it.hellinger_distance([1.0, 0.0], [0.0, 1.0])
1.0
```

---

## **Utility Functions**

### `validate_distribution(p, tolerance=1e-9)`
Validate that a list represents a valid probability distribution.

**Parameters:**
- `p` (List[float]): List of probability values
- `tolerance` (float): Tolerance for sum check (default: 1e-9)

**Returns:**
- `None`: Raises ValueError if invalid

**Example:**
```python
>>> it.validate_distribution([0.3, 0.7])  # Valid
>>> it.validate_distribution([0.5, 0.6])  # Raises ValueError
```

---

### `normalize_distribution(p)`
Normalize a list of non-negative values to form a probability distribution.

**Parameters:**
- `p` (List[float]): List of non-negative values

**Returns:**
- `List[float]`: Normalized probability distribution

**Example:**
```python
>>> it.normalize_distribution([1, 2, 3])
[0.16666666666666666, 0.3333333333333333, 0.5]
```

---

## **Error Handling**

All MetricaX functions include comprehensive error handling:

### **Common Exceptions:**
- `ValueError`: Invalid input parameters (negative probabilities, distributions that don't sum to 1, etc.)
- `TypeError`: Incorrect parameter types
- `ZeroDivisionError`: Division by zero in mathematical operations (handled gracefully)

### **Input Validation:**
- Probability distributions are validated to sum to 1 (within tolerance)
- All probability values must be non-negative and finite
- Parameters must be within valid mathematical ranges

### **Numerical Stability:**
- Safe logarithm computation with epsilon handling
- Overflow protection for extreme parameter values
- Graceful handling of edge cases (zero probabilities, etc.)

---

**For complete examples and advanced usage, see the module-specific README files and the TECHNICAL_GUIDE.md.**
