name: MetricaX Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]
        exclude:
          # Reduce matrix size for faster CI
          - os: windows-latest
            python-version: "3.8"
          - os: macos-latest
            python-version: "3.8"

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-benchmark
        pip install -e .
    
    - name: Run tests with coverage
      run: |
        pytest metricax/ -v --cov=metricax --cov-report=xml --cov-report=html
    
    - name: Run benchmarks
      run: |
        pytest metricax/ --benchmark-only --benchmark-sort=mean
    
    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.python-version == '3.10'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install linting dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
    
    - name: Run flake8
      run: flake8 metricax/ --count --select=E9,F63,F7,F82 --show-source --statistics
    
    - name: Check black formatting
      run: black --check metricax/
    
    - name: Check import sorting
      run: isort --check-only metricax/
    
    - name: Run type checking
      run: mypy metricax/ --ignore-missing-imports

  docs:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install documentation dependencies
      run: |
        python -m pip install --upgrade pip
        pip install sphinx sphinx-rtd-theme
        pip install -e .
    
    - name: Build documentation
      run: |
        cd docs/
        make html
    
    - name: Check documentation links
      run: |
        cd docs/
        make linkcheck

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install security scanning tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run bandit security scan
      run: bandit -r metricax/
    
    - name: Check for known security vulnerabilities
      run: safety check

  performance:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install performance testing dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-benchmark memory-profiler
        pip install -e .
    
    - name: Run performance benchmarks
      run: |
        pytest metricax/ --benchmark-only --benchmark-json=benchmark.json
    
    - name: Memory profiling
      run: |
        python -m memory_profiler metricax/bayesian/examples/ab_testing.py
        python -m memory_profiler metricax/info_theory/examples/entropy_example.py
